<?php

/**
 * Examples of how to run the WebSearchCountryCodeLeadsJob with different time ranges
 *
 * This file shows how to dispatch the job with various configurations.
 * You can run these examples from <PERSON><PERSON> Tinker or create Artisan commands.
 */

use App\Jobs\WebSearchCountryCodeLeadsJob;

// Example 1: Run with default settings (last 7 days - newest first)
echo "Example 1: Default settings (last 7 days - newest first)\n";
$job1 = new WebSearchCountryCodeLeadsJob;
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch();

// Example 2: Run with explicit week time range - NEWEST FIRST
echo "Example 2: Explicit last 7 days (week) - newest first\n";
$job2 = new WebSearchCountryCodeLeadsJob('week', ['bing', 'google']);
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('week', ['bing', 'google']);

// Example 3: Run with current day only
echo "Example 3: Current day only\n";
$job3 = new WebSearchCountryCodeLeadsJob('day', ['bing', 'google']);
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('day', ['bing', 'google']);

// Example 4: Run with 30 days (month) time range - NEWEST FIRST
echo "Example 4: Last 30 days (month) - newest first\n";
$job4 = new WebSearchCountryCodeLeadsJob('month', ['bing', 'google']);
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('month', ['bing', 'google']);

// Example 5: Run only Google with default (week) range
echo "Example 5: Google only, default (last 7 days)\n";
$job5 = new WebSearchCountryCodeLeadsJob('week', ['google']);
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);

// Example 6: Run only Bing with default (week) range
echo "Example 6: Bing only, default (last 7 days)\n";
$job6 = new WebSearchCountryCodeLeadsJob('week', ['bing']);
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('week', ['bing']);

// Example 7: Run with just time range (uses default engines)
echo "Example 7: Week time range with default engines\n";
$job7 = new WebSearchCountryCodeLeadsJob('week');
// To dispatch: WebSearchCountryCodeLeadsJob::dispatch('week');

/**
 * ARTISAN COMMAND EXAMPLES
 *
 * You can create an Artisan command to easily run these jobs:
 *
 * php artisan make:command RunWebSearchJob
 *
 * Then in the command handle() method:
 */

/*
// For last 7 days (default)
WebSearchCountryCodeLeadsJob::dispatch();

// For current day only
WebSearchCountryCodeLeadsJob::dispatch('day', ['bing', 'google']);

// For last 30 days (newest first)
WebSearchCountryCodeLeadsJob::dispatch('month', ['bing', 'google']);

// For specific time range with default engines
WebSearchCountryCodeLeadsJob::dispatch('week');
*/

/**
 * LARAVEL TINKER EXAMPLES
 *
 * Run these commands in Laravel Tinker (php artisan tinker):
 */

/*
// Last 7 days (newest first) - DEFAULT
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch();

// Current day only
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('day', ['bing', 'google']);

// Last 30 days (newest first)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('month', ['bing', 'google']);

// Test with only one engine for faster testing
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week', ['google']);

// Just specify time range (uses default engines)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch('week');
*/

/**
 * QUEUE WORKER
 *
 * Don't forget to run the queue worker to process the jobs:
 * php artisan queue:work
 */
echo "\nAll examples created successfully!\n";
echo "Default is now 'week' (last 7 days, newest first).\n";
echo "Use Laravel Tinker or create Artisan commands to dispatch these jobs.\n";
echo "Remember to run 'php artisan queue:work' to process the jobs.\n";
