# WebSearch Time Range Feature

## Overview

The `WebSearchCountryCodeLeadsJob` now supports configurable time ranges for search results. You can search for results from the current day, last 7 days, or last 30 days, with newest results appearing first.

## Available Time Ranges

- **`day`** - Current day only (default)
- **`week`** - Last 7 days (newest first) ⭐ **RECOMMENDED FOR YOUR USE CASE**
- **`month`** - Last 30 days (newest first)

## How to Use

### 1. Using Artisan Command (Easiest)

```bash
# Last 7 days (newest first) - RECOMMENDED
php artisan websearch:run --time=week

# Current day only
php artisan websearch:run --time=day

# Last 30 days (newest first)
php artisan websearch:run --time=month

# Specific search engines with time range
php artisan websearch:run --engines=google --time=week
php artisan websearch:run --engines=bing --engines=google --time=week

# Run synchronously (for testing)
php artisan websearch:run --time=week --sync
```

### 2. Using Laravel Tinker

```php
// Start tinker
php artisan tinker

// Last 7 days (newest first) - WHAT YOU WANT!
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'week');

// Current day only
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'day');

// Last 30 days (newest first)
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'month');

// Only Google, last 7 days
App\Jobs\WebSearchCountryCodeLeadsJob::dispatch(['google'], 'week');
```

### 3. In Your Code

```php
use App\Jobs\WebSearchCountryCodeLeadsJob;

// Last 7 days (newest first)
WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'week');

// Current day only (default)
WebSearchCountryCodeLeadsJob::dispatch();

// Last 30 days (newest first)
WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'month');
```

## Technical Details

### Search Engine Parameters

The job automatically configures the correct parameters for each search engine:

**Google:**
- `day`: `tbs=qdr:d` (current day)
- `week`: `tbs=qdr:w` (last 7 days)
- `month`: `tbs=qdr:m` (last 30 days)

**Bing:**
- `day`: `time=day` (current day)
- `week`: `time=week` (last 7 days)
- `month`: `time=month` (last 30 days)

### Result Ordering

Search engines typically return results with newest first by default, which is exactly what you want for finding recent Shopify stores.

### Logging

The job logs include the time range setting:

```
[WebSearchCountryCodeLeadsJob] Starting job. {
    "search_engines": ["bing", "google"],
    "time_range": "week",
    "environment": "local",
    "test_mode": true
}
```

## Queue Processing

Don't forget to run the queue worker to process the jobs:

```bash
php artisan queue:work
```

## Examples for Your Use Case

Since you want to get results from the last 7 days with newest first, use:

```bash
# Command line
php artisan websearch:run --time=week

# Or in code
WebSearchCountryCodeLeadsJob::dispatch(['bing', 'google'], 'week');
```

This will:
1. Search for Shopify stores in the last 7 days
2. Return results with newest first
3. Cover more ground than just current day
4. Still be recent enough to be relevant

## Error Handling

If you provide an invalid time range, the job will:
1. Log a warning
2. Default to 'day' time range
3. Continue processing

Valid time ranges are: `day`, `week`, `month`

## Performance Considerations

- **`week`** provides a good balance between coverage and relevance
- **`month`** may return more results but could include older, less relevant stores
- **`day`** is fastest but may miss stores that were indexed yesterday

For your use case of finding new Shopify stores, **`week`** is the recommended setting.
