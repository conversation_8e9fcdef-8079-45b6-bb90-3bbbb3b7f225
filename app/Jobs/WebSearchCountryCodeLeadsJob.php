<?php

namespace App\Jobs;

use App\Helpers\DomainParserHelper;
use App\Helpers\LeadBatchProcessorHelper;

use App\Helpers\WebSearch\BrightDataSerpHelper;
use App\Helpers\WebSearch\SerpApiHelper;
use App\Helpers\WebSearch\SerpStackHelper;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WebSearchCountryCodeLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The search engines to use.
     */
    protected array $searchEngines;

    /**
     * The time range for search results.
     */
    protected string $timeRange;

    // No longer using countryCodes property - each engine uses its own country codes

    /**
     * Create a new job instance.
     *
     * @param  array  $searchEngines  The search engines to use (supported: 'bing', 'google')
     *                                Note:
     *                                - 'bing' can use either SerpStackHelper or BrightDataSerpHelper (if configured)
     *                                - 'google' can use either SerpApiHelper or BrightDataSerpHelper (if configured)
     *                                All search engines are configured to return 100 results with newest results first.
     * @param  string  $timeRange     The time range for search results (supported: 'day', 'week', 'month')
     *                                - 'day': Current day only
     *                                - 'week': Last 7 days (newest first) [DEFAULT]
     *                                - 'month': Last 30 days (newest first)
     */
    public function __construct(
        array $searchEngines = ['bing', 'google'],
        string $timeRange = 'week'
    ) {
        $this->searchEngines = $searchEngines;
        $this->timeRange = $this->validateTimeRange($timeRange);
    }

    /**
     * Validate and normalize the time range parameter.
     *
     * @param  string  $timeRange  The time range to validate
     * @return string The validated time range
     */
    protected function validateTimeRange(string $timeRange): string
    {
        $validRanges = ['day', 'week', 'month'];
        $normalizedRange = strtolower(trim($timeRange));

        if (!in_array($normalizedRange, $validRanges)) {
            Log::warning('[WebSearchCountryCodeLeadsJob] Invalid time range provided, defaulting to "day".', [
                'provided_range' => $timeRange,
                'valid_ranges' => $validRanges,
            ]);
            return 'day';
        }

        return $normalizedRange;
    }

    /**
     * Get time filter parameters for different search engines.
     *
     * @param  string  $engine  The search engine name
     * @return array The time filter parameters
     */
    protected function getTimeFilterParams(string $engine): array
    {
        switch ($engine) {
            case 'google':
                // Google uses 'tbs' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['tbs' => 'qdr:w']; // Last week (7 days)
                    case 'month':
                        return ['tbs' => 'qdr:m']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['tbs' => 'qdr:d']; // Current day
                }

            case 'bing':
                // Bing uses 'time' parameter for time filtering
                switch ($this->timeRange) {
                    case 'week':
                        return ['time' => 'week']; // Last week (7 days)
                    case 'month':
                        return ['time' => 'month']; // Last month (30 days)
                    case 'day':
                    default:
                        return ['time' => 'day']; // Current day
                }

            default:
                return [];
        }
    }

    /**
     * Execute the job.
     */
    public function handle()
    {
        $environment = app()->environment();
        Log::info('[WebSearchCountryCodeLeadsJob] Starting job.', [
            'search_engines' => $this->searchEngines,
            'time_range' => $this->timeRange,
            'environment' => $environment,
            'test_mode' => ($environment !== 'production'),
        ]);

        $allParsedLeads = [];
        $totalLeadsCollected = 0;

        // Initialize search helpers with all configuration
        $searchHelpers = $this->initializeSearchHelpers();
        if (empty($searchHelpers)) {
            Log::error('[WebSearchCountryCodeLeadsJob] No search helpers could be initialized. Aborting job.');

            return;
        }

        // Process each search engine with its appropriate country codes
        foreach ($searchHelpers as $engineName => $helperConfig) {
            // Get the helper class, country codes and search parameters
            $helper = $helperConfig['class'];
            $helperType = $helperConfig['type'];
            $countryCodes = $helperConfig['country_codes'];
            $searchParams = $helperConfig['search_params'];

            Log::info("[WebSearchCountryCodeLeadsJob] Processing {$engineName} search with ".count($countryCodes).' country codes');

            // Process each country code for this engine
            foreach ($countryCodes as $countryCode) {
                // Build the query using site: operators for better search engine compatibility
                $query = 'site:.'.$countryCode.' inurl:"/collections/all/" -site:shopify.com -site:stackoverflow.com -site:help.shopify.com -site:accounts.shopify.com -site:shopify.dev -site:myshopify.com';

                Log::info("[WebSearchCountryCodeLeadsJob] Processing {$engineName} search for country code: {$countryCode} with query: {$query}");

                // Prepare search parameters with the actual country code
                $params = $this->prepareSearchParams($searchParams, $countryCode);

                // Perform the search
                try {
                    // Perform the search based on the helper type
                    $results = null;

                    if ($helperType === 'brightdata') {
                        $results = $helper->search($engineName, $query, strtoupper($countryCode), $params);
                    } elseif ($helperType === 'serpstack') {
                        $results = $helper->search($query, $params);
                    } elseif ($helperType === 'serpapi') {
                        $results = $helper->search('google', $query, strtoupper($countryCode), $params);
                    }

                    if (! empty($results)) {
                        // Process the results and extract domain information
                        $parsedResults = $this->processSearchResults($results, $engineName);

                        if (! empty($parsedResults)) {
                            $allParsedLeads = array_merge($allParsedLeads, $parsedResults);
                            $totalLeadsCollected += count($parsedResults);

                            Log::info("[WebSearchCountryCodeLeadsJob] Collected {$totalLeadsCollected} leads so far from {$engineName} for country code: {$countryCode}");
                        }
                    }
                } catch (Exception $e) {
                    Log::error("[WebSearchCountryCodeLeadsJob] Error performing search with {$engineName} for country code {$countryCode}: ".$e->getMessage());
                }

                // Add a small delay between requests to avoid rate limiting
                sleep(1);
            }
        }

        // Process all collected leads
        if (! empty($allParsedLeads)) {
            Log::info('[WebSearchCountryCodeLeadsJob] Passing '.count($allParsedLeads).' collected leads to batch processor.');
            $batchProcessor = new LeadBatchProcessorHelper;
            $batchProcessor->processLeads($allParsedLeads);
        } else {
            Log::info('[WebSearchCountryCodeLeadsJob] No leads collected to process.');
        }

        Log::info("[WebSearchCountryCodeLeadsJob] Job finished. Total leads prepared for batching: {$totalLeadsCollected}");
    }

    /**
     * Initialize the search helpers for each enabled search engine.
     * This method also configures country codes and search parameters for each engine.
     *
     * @return array The initialized search helpers with configuration
     *
     * @throws Exception
     */
    protected function initializeSearchHelpers(): array
    {
        $helpers = [];

        // Get API keys and configurations
        $brightDataConfig = [
            'api_key' => config('crawler.brightdata_api_key'),
            'customer_id' => config('crawler.brightdata_customer_id'),
            'zone_id' => config('crawler.brightdata_zone_id'),
        ];

        $serpApiKey = config('crawler.serpapi_api_key');
        $serpStackApiKey = config('crawler.serpstack_api_key');

        // Check if BrightData is configured
        $brightDataConfigured = ! empty($brightDataConfig['api_key']) &&
                               ! empty($brightDataConfig['customer_id']) &&
                               ! empty($brightDataConfig['zone_id']);

        // Create BrightData helper if configured (will be reused for both Google and Bing)
        $brightDataHelper = null;
        if ($brightDataConfigured) {
            $brightDataHelper = new BrightDataSerpHelper(
                $brightDataConfig['api_key'],
                $brightDataConfig['customer_id'],
                $brightDataConfig['zone_id']
            );
            Log::info('[WebSearchCountryCodeLeadsJob] Initialized BrightDataSerpHelper');
        }

        // Common TLDs and special domains to include for all engines
        $commonTlds = ['com', 'eu', 'nu', 'store', 'net', 'online', 'org', 'info', 'biz', 'site', 'website', 'tech', 'app', 'blog'];

        // Initialize helpers for each requested search engine
        foreach ($this->searchEngines as $engine) {
            try {
                $engineName = strtolower($engine);
                $helperClass = null;
                $helperType = '';
                $countryCodes = [];

                // Define search parameters for this engine
                $searchParams = [];

                switch ($engineName) {
                    case 'bing':
                        if ($brightDataConfigured) {
                            $helperClass = $brightDataHelper;
                            $helperType = 'brightdata';

                            // Get country codes
                            $countryCodes = array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchCountryCodeLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            // Get time filter parameters for Bing
                            $timeParams = $this->getTimeFilterParams('bing');

                            $searchParams = [
                                'engine' => 'bing',
                                'country_code' => '{COUNTRY_CODE}', // Will be replaced with actual country code
                                'count' => 100,
                                'first' => 1,    // First page of results
                                'mkt' => 'en-US', // English US market
                            ];

                            // Merge time filter parameters
                            $searchParams = array_merge($searchParams, $timeParams);

                            Log::info('[WebSearchCountryCodeLeadsJob] Using BrightDataSerpHelper for Bing');
                        } elseif (! empty($serpStackApiKey)) {
                            $helperClass = new SerpStackHelper($serpStackApiKey, true);
                            $helperType = 'serpstack';

                            // Get country codes
                            $countryCodes = array_map('strtolower', SerpStackHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchCountryCodeLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            // Get time filter parameters for Bing
                            $timeParams = $this->getTimeFilterParams('bing');

                            $searchParams = [
                                'engine' => 'bing',
                                'country_code' => '{COUNTRY_CODE}', // Will be replaced with actual country code
                                'num' => 100,
                            ];

                            // Merge time filter parameters
                            $searchParams = array_merge($searchParams, $timeParams);

                            Log::info('[WebSearchCountryCodeLeadsJob] Using SerpStackHelper for Bing');
                        } else {
                            Log::warning('[WebSearchCountryCodeLeadsJob] No API keys configured for Bing search. Skipping.');

                            continue 2; // Skip to next engine
                        }
                        break;

                    case 'google':
                        if ($brightDataConfigured) {
                            $helperClass = $brightDataHelper;
                            $helperType = 'brightdata';

                            // Get country codes
                            $countryCodes = array_map('strtolower', BrightDataSerpHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchCountryCodeLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            // Get time filter parameters for Google
                            $timeParams = $this->getTimeFilterParams('google');

                            $searchParams = [
                                'gl' => '{COUNTRY_CODE}', // Country code for Google (e.g., 'dk', 'us')
                                'num' => 100,
                                'hl' => 'en',     // English language
                                'start' => 0,     // First page of results
                                'brd_mobile' => 0, // Desktop user agent
                            ];

                            // Merge time filter parameters
                            $searchParams = array_merge($searchParams, $timeParams);

                            Log::info('[WebSearchCountryCodeLeadsJob] Using BrightDataSerpHelper for Google');
                        } elseif (! empty($serpApiKey)) {
                            $helperClass = new SerpApiHelper($serpApiKey);
                            $helperType = 'serpapi';

                            // Get country codes
                            $countryCodes = array_map('strtolower', SerpApiHelper::SUPPORTED_COUNTRY_CODES);

                            // Process country codes
                            // Merge with common TLDs
                            $countryCodes = array_merge($countryCodes, $commonTlds);

                            // Remove duplicates and sort
                            $countryCodes = array_unique($countryCodes);
                            sort($countryCodes);

                            // If not in production environment, only use one country code for testing
                            if (app()->environment() !== 'production') {
                                Log::info("[WebSearchCountryCodeLeadsJob] Non-production environment detected. Using only one country code for {$engineName}.");
                                $countryCodes = ['dk']; // Use Denmark as the test country
                            }

                            // Get time filter parameters for Google
                            $timeParams = $this->getTimeFilterParams('google');

                            $searchParams = [
                                'gl' => '{COUNTRY_CODE}', // Country code for Google
                                'country_code' => '{COUNTRY_CODE}', // For SerpApiHelper
                                'num' => 100,
                            ];

                            // Merge time filter parameters
                            $searchParams = array_merge($searchParams, $timeParams);

                            Log::info('[WebSearchCountryCodeLeadsJob] Using SerpApiHelper for Google');
                        } else {
                            Log::warning('[WebSearchCountryCodeLeadsJob] No API keys configured for Google search. Skipping.');

                            continue 2; // Skip to next engine
                        }
                        break;

                    default:
                        Log::warning("[WebSearchCountryCodeLeadsJob] Unsupported search engine: {$engineName}. Skipping.");

                        continue 2; // Skip to next engine
                }

                // Add the helper to the helpers array with all configuration in one place
                $helpers[$engineName] = [
                    'class' => $helperClass,
                    'type' => $helperType,
                    'engine' => $engineName,
                    'country_codes' => $countryCodes,
                    'search_params' => $searchParams,
                ];

            } catch (Exception $e) {
                Log::error("[WebSearchCountryCodeLeadsJob] Error initializing search helper for {$engine}: ".$e->getMessage());
            }
        }

        return $helpers;
    }

    /**
     * Prepare search parameters by replacing placeholders with actual values.
     *
     * @param  array  $params  The search parameters with placeholders
     * @param  string  $countryCode  The country code to use
     * @return array The prepared search parameters
     */
    protected function prepareSearchParams(array $params, string $countryCode): array
    {
        $result = [];

        foreach ($params as $key => $value) {
            if (is_string($value) && $value === '{COUNTRY_CODE}') {
                // For most APIs, use lowercase country codes
                $result[$key] = strtolower($countryCode);
            } else {
                $result[$key] = $value;
            }
        }

        return $result;
    }

    /**
     * Process the search results and extract domain information.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The processed results with domain information
     */
    protected function processSearchResults(array $results, string $engineName): array
    {
        $processedResults = [];

        try {
            $organicResults = $this->extractOrganicResults($results, $engineName);

            if (empty($organicResults)) {
                Log::info("[WebSearchCountryCodeLeadsJob] No organic results found for {$engineName}.");

                return [];
            }

            foreach ($organicResults as $result) {
                $url = $this->extractUrlFromResult($result, $engineName);

                if (empty(trim($url))) {
                    continue;
                }

                $domainInfo = DomainParserHelper::getProcessedDomainInformation($url);

                if ($domainInfo) {
                    $processedResults[] = $domainInfo;
                }
            }

            Log::info('[WebSearchCountryCodeLeadsJob] Processed '.count($processedResults)." results from {$engineName}.");

        } catch (Exception $e) {
            Log::error("[WebSearchCountryCodeLeadsJob] Error processing search results from {$engineName}: ".$e->getMessage());
        }

        return $processedResults;
    }

    /**
     * Extract organic results from the search results based on the search engine.
     *
     * @param  array  $results  The search results
     * @param  string  $engineName  The name of the search engine
     * @return array The organic results
     */
    protected function extractOrganicResults(array $results, string $engineName): array
    {
        Log::debug("[WebSearchCountryCodeLeadsJob] Extracting organic results for {$engineName}.", [
            'engine' => $engineName,
            'result_keys' => array_keys($results),
            'result_structure' => $this->getResultStructureDebug($results),
        ]);

        switch ($engineName) {
            case 'bing':
                // Bing via BrightData returns results in 'organic' key
                $organicResults = $results['organic'] ?? [];
                break;

            case 'google':
                // Google via BrightData returns results in 'organic' key
                $organicResults = $results['organic'] ?? [];
                break;

            default:
                $organicResults = [];
        }

        Log::debug("[WebSearchCountryCodeLeadsJob] Extracted {$engineName} organic results.", [
            'engine' => $engineName,
            'organic_results_count' => count($organicResults),
            'first_result_keys' => !empty($organicResults) ? array_keys($organicResults[0] ?? []) : [],
        ]);

        return $organicResults;
    }

    /**
     * Extract the URL from a search result based on the search engine.
     *
     * @param  array  $result  The search result
     * @param  string  $engineName  The name of the search engine
     * @return string The extracted URL
     */
    protected function extractUrlFromResult(array $result, string $engineName): string
    {
        Log::debug("[WebSearchCountryCodeLeadsJob] Extracting URL from {$engineName} result.", [
            'engine' => $engineName,
            'result_keys' => array_keys($result),

            'url_field_link' => $result['link'] ?? 'not_found',
        ]);

        switch ($engineName) {
            case 'bing':
            case 'google':
                // BrightData returns URLs in 'link' field
                return $result['link'] ?? '';

            default:
                return '';
        }
    }

    /**
     * Get a debug representation of the result structure.
     *
     * @param  array  $results  The search results
     * @return array Debug information about the structure
     */
    protected function getResultStructureDebug(array $results): array
    {
        $debug = [];

        foreach ($results as $key => $value) {
            if (is_array($value)) {
                $debug[$key] = [
                    'type' => 'array',
                    'count' => count($value),
                    'keys' => is_array($value) && !empty($value) ? array_keys($value) : [],
                ];

                // If it's a numeric array, show structure of first element
                if (isset($value[0]) && is_array($value[0])) {
                    $debug[$key]['first_element_keys'] = array_keys($value[0]);
                }
            } else {
                $debug[$key] = [
                    'type' => gettype($value),
                    'value' => is_string($value) ? substr($value, 0, 100) : $value,
                ];
            }
        }

        return $debug;
    }
}
