<?php

namespace App\Console\Commands;

use App\Jobs\WebSearchCountryCodeLeadsJob;
use Illuminate\Console\Command;

class RunWebSearchJob extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'websearch:run 
                            {--engines=* : Search engines to use (bing, google)}
                            {--time=week : Time range (day, week, month)}
                            {--sync : Run synchronously instead of queuing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run the WebSearch Country Code Leads Job with configurable time range (default: week)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get engines from options or use default
        $engines = $this->option('engines');
        if (empty($engines)) {
            $engines = ['bing', 'google'];
        }

        // Get time range
        $timeRange = $this->option('time');

        // Validate time range
        $validRanges = ['day', 'week', 'month'];
        if (! in_array($timeRange, $validRanges)) {
            $this->error("Invalid time range: {$timeRange}");
            $this->info('Valid options: '.implode(', ', $validRanges));

            return 1;
        }

        // Validate engines
        $validEngines = ['bing', 'google'];
        foreach ($engines as $engine) {
            if (! in_array($engine, $validEngines)) {
                $this->error("Invalid search engine: {$engine}");
                $this->info('Valid options: '.implode(', ', $validEngines));

                return 1;
            }
        }

        $this->info('Starting WebSearch job with:');
        $this->info('- Engines: '.implode(', ', $engines));
        $this->info("- Time range: {$timeRange}");

        if ($timeRange === 'week') {
            $this->info('- Will search last 7 days (newest first) [DEFAULT]');
        } elseif ($timeRange === 'month') {
            $this->info('- Will search last 30 days (newest first)');
        } else {
            $this->info('- Will search current day only');
        }

        if ($this->option('sync')) {
            $this->info('Running synchronously...');
            $job = new WebSearchCountryCodeLeadsJob($timeRange, $engines);
            $job->handle();
            $this->info('Job completed!');
        } else {
            $this->info('Dispatching to queue...');
            WebSearchCountryCodeLeadsJob::dispatch($timeRange, $engines);
            $this->info('Job dispatched to queue!');
            $this->info("Run 'php artisan queue:work' to process the job.");
        }

        return 0;
    }
}
